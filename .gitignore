# Created by https://www.toptal.com/developers/gitignore/api/android,kotlin,gradle,jetbrains
# Edit at https://www.toptal.com/developers/gitignore?templates=android,kotlin,gradle,jetbrains

### Android ###
# Built application files
*.apk
*.aab
*.ap_

# Files for the ART/Dalvik VM
*.dex

# Java class files
*.class

# Generated files
bin/
gen/
out/

# Gradle files
.gradle/
build/

# Local configuration file (sdk path, etc)
local.properties

# Proguard folder generated by Eclipse
proguard/

# Log Files
*.log

# Android Studio Navigation editor files
.navigation/

# Android Studio captures folder
captures/

# Keystore files
*.jks
*.keystore

# External native build folder generated in Android Studio 2.2 and later
.externalNativeBuild/

# Google Services (e.g. APIs or Firebase)
google-services.json

# Freeline
freeline.py
freeline/
freeline_project_description.json

# fastlane
fastlane/metadata/android/en-US/images

# Crashlytics plugin
com_crashlytics_export_strings.xml


### Gradle ###
.gradle
build/
!gradle/wrapper/gradle-wrapper.jar
!**/src/main/**/build/
!**/src/test/**/build/

### JetBrains ###
# Covers JetBrains IDEs: IntelliJ IDEA, PhpStorm, PyCharm, RubyMine, WebStorm and AppCode
# Reference: https://intellij-support.jetbrains.com/hc/en-us/articles/206544839

# User-specific stuff
.idea/**/workspace.xml
.idea/**/tasks.xml
.idea/**/usage.statistics.xml
.idea/**/dictionaries
.idea/**/shelf

# Generated files
.idea/**/contentModel.xml

# Sensitive or high-churn files
.idea/**/dataSources/
.idea/**/dataSources.ids
.idea/**/dataSources.local.xml
.idea/**/sqlDataSources.xml
.idea/**/dynamic.xml
.idea/**/uiDesigner.xml
.idea/**/dbnavigator.xml

# Gradle
.idea/gradle.xml
.idea/libraries

# Gradle and Maven with auto-import
# When using Gradle or Maven with auto-import, you should exclude module files,
# since they will be recreated, and may cause churn.  Uncomment if using
# auto-import.
# .idea/modules.xml
# .idea/*.iml
# .idea/modules

# Caching files
.idea/**/caches/
.idea/**/jars/

# Editor-based Rest Client
.idea/httpRequests

# Android Studio profiling
.idea/profiles

# File-based project format
*.iws

# Plugin-specific files

# IntelliJ
/out/

# mpeltonen/sbt-idea plugin
.idea_modules/

# JIRA plugin
atlassian-ide-plugin.xml

# Cursive Clojure plugin
.idea/repls.history

# Crashlytics plugin (for Android Studio and IntelliJ)
com_crashlytics_export_strings.xml

# EditorConfig plugin
.editorconfig

# Plugin Data folder
.idea/dataSources/

# Plugin specific files (updated for 2021.1)
.idea/sonarlint/
.idea/codeStyles/Project.xml

# Android Studio 3.1+
.idea/caches/build_file_checksums.ser

### Kotlin ###
# Compiled class files
*.class

# Compiled native library files
*.dll
*.so
*.dylib

# Jar files
*.jar

# Log files
*.log

# Build metadata
.build/
build/

# Kotlin project files
*.kproj
*.kproj.user
*.sln
*.suo

# IDEA project files
.idea/
*.iml
*.ipr
*.iws

# kotlin-gradle-plugin cache
.kotlin/

# End of https://www.toptal.com/developers/gitignore/api/android,kotlin,gradle,jetbrains
