plugins {
    alias(libs.plugins.kotlin.multiplatform)
    alias(libs.plugins.android.library)
    alias(libs.plugins.jetbrains.compose)
}

kotlin {
    androidTarget {
        compilations.all {
            kotlinOptions {
                jvmTarget = "1.8"
            }
        }
    }

    jvm("desktop") {
        // Desktop specific configurations if any
    }

    sourceSets {
        val commonMain by getting {
            dependencies {
                api(libs.jetbrains.compose.runtime)
                api(libs.jetbrains.compose.foundation)
                api(libs.jetbrains.compose.material) // Or material3
                api(libs.jetbrains.compose.ui)
                api(libs.kotlinx.coroutines.core)
            }
        }
        val androidMain by getting {
            dependencies {
                // Android specific dependencies can be added here if needed
                // For example, if using AndroidX libraries not covered by commonMain
                // api(libs.androidx.compose.ui) // Example
            }
        }
        val desktopMain by getting {
            dependencies {
                api(libs.jetbrains.compose.desktop.currentOs)
            }
        }
        val commonTest by getting {
            dependencies {
                implementation(kotlin("test"))
            }
        }
    }
}

android {
    namespace = "com.example.shared"
    compileSdk = libs.versions.compileSdk.get().toInt()
    defaultConfig {
        minSdk = libs.versions.minSdk.get().toInt()
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }
    // Ensure the Compose compiler plugin is correctly applied for Android.
    // The org.jetbrains.compose plugin should handle this, but verify compatibility
    // between Kotlin version (libs.versions.kotlin) and Compose compiler (libs.versions.composeCompiler).
}
