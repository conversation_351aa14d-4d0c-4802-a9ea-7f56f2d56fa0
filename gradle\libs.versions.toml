[versions]

# Kotlin
kotlin = "2.0.0"

# Android
androidGradlePlugin = "8.4.0"
compileSdk = "34"
minSdk = "24"
targetSdk = "34"

# Compose Multiplatform
compose = "1.6.0" # This version will be used for JetBrains Compose as well
composeCompiler = "1.5.10" # Compatible with Kotlin 2.0.0
composeMaterial3 = "1.2.1" # A version compatible with other compose libs

# Other libraries
coroutines = "1.7.3"
activityCompose = "1.8.2"


[libraries]

# Kotlin Standard Library
kotlin-stdlib = { module = "org.jetbrains.kotlin:kotlin-stdlib", version.ref = "kotlin" }

# Coroutines
kotlinx-coroutines-core = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-core", version.ref = "coroutines" }

# AndroidX Compose libraries
androidx-compose-ui = { module = "androidx.compose.ui:ui", version.ref = "compose" }
androidx-compose-ui-tooling-preview = { module = "androidx.compose.ui:ui-tooling-preview", version.ref = "compose" }
androidx-compose-material = { module = "androidx.compose.material:material", version.ref = "compose" }
androidx-compose-material3 = { module = "androidx.compose.material3:material3", version.ref = "composeMaterial3"}

# JetBrains Compose Multiplatform libraries
jetbrains-compose-runtime = { module = "org.jetbrains.compose.runtime:runtime", version.ref = "compose" }
jetbrains-compose-foundation = { module = "org.jetbrains.compose.foundation:foundation", version.ref = "compose" }
jetbrains-compose-material = { module = "org.jetbrains.compose.material:material", version.ref = "compose" } # Or material3
jetbrains-compose-ui = { module = "org.jetbrains.compose.ui:ui", version.ref = "compose" }
jetbrains-compose-desktop-currentOs = { module = "org.jetbrains.compose.desktop:desktop-jvm", version.ref = "compose" }

# Android specific libraries
androidx-activity-compose = { module = "androidx.activity:activity-compose", version.ref = "activityCompose" }

[plugins]

# Kotlin
kotlin-multiplatform = { id = "org.jetbrains.kotlin.multiplatform", version.ref = "kotlin" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
# kotlin-cocoapods = { id = "org.jetbrains.kotlin.native.cocoapods", version.ref = "kotlin" } # If you add iOS

# Android
android-application = { id = "com.android.application", version.ref = "androidGradlePlugin" }
android-library = { id = "com.android.library", version.ref = "androidGradlePlugin" }

# Compose Multiplatform
jetbrains-compose = { id = "org.jetbrains.compose", version.ref = "compose" }
