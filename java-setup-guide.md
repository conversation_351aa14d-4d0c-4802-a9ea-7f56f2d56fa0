# Java 环境配置指南

## 当前状态
- ✅ JDK 21 已安装
- ❌ JAVA_HOME 环境变量未设置
- ❌ Java 不在系统 PATH 中

## 解决方案

### 方法 1: 查找 JDK 安装位置

请在文件资源管理器中查找 JDK 21 的安装位置，常见位置包括：

1. `C:\Program Files\Java\jdk-21`
2. `C:\Program Files\Eclipse Adoptium\jdk-21.x.x.x-hotspot`
3. `C:\Program Files\OpenJDK\jdk-21`
4. `C:\Program Files\Amazon Corretto\jdk21.x.x_x`
5. `C:\Users\<USER>\.jdks\openjdk-21`

### 方法 2: 设置环境变量

找到 JDK 安装路径后，设置环境变量：

#### 临时设置（当前会话）
```powershell
# 替换为您的实际 JDK 路径
$env:JAVA_HOME = "C:\Program Files\Java\jdk-21"
$env:PATH = "$env:JAVA_HOME\bin;$env:PATH"
```

#### 永久设置（推荐）
1. 右键点击"此电脑" → "属性"
2. 点击"高级系统设置"
3. 点击"环境变量"
4. 在"系统变量"中点击"新建"：
   - 变量名：`JAVA_HOME`
   - 变量值：您的 JDK 安装路径（如 `C:\Program Files\Java\jdk-21`）
5. 编辑 PATH 变量，添加：`%JAVA_HOME%\bin`

### 方法 3: 使用 PowerShell 查找 JDK

```powershell
# 搜索 JDK 安装
Get-ChildItem -Path "C:\Program Files" -Filter "*jdk*" -Directory -Recurse -Depth 2
Get-ChildItem -Path "C:\Program Files (x86)" -Filter "*jdk*" -Directory -Recurse -Depth 2

# 或者搜索 java.exe
Get-ChildItem -Path "C:\" -Filter "java.exe" -Recurse -Depth 4 -ErrorAction SilentlyContinue
```

## 验证设置

设置完成后，重新打开 PowerShell 并运行：

```powershell
java -version
echo $env:JAVA_HOME
```

应该看到类似输出：
```
openjdk version "21.0.x" 2024-xx-xx
OpenJDK Runtime Environment (build 21.0.x+xx)
OpenJDK 64-Bit Server VM (build 21.0.x+xx, mixed mode, sharing)
```

## 构建项目

环境变量设置完成后，可以运行：

```bash
./gradlew build
```

## 常见问题

1. **找不到 JDK**: 可能安装在用户目录下，检查 `C:\Users\<USER>\.jdks\`
2. **权限问题**: 以管理员身份运行 PowerShell
3. **路径包含空格**: 确保路径用引号包围

## 快速解决方案

如果您知道 JDK 的安装路径，可以直接在当前 PowerShell 会话中设置：

```powershell
# 示例：假设 JDK 安装在 C:\Program Files\Java\jdk-21
$env:JAVA_HOME = "C:\Program Files\Java\jdk-21"
$env:PATH = "$env:JAVA_HOME\bin;$env:PATH"

# 然后运行构建
./gradlew build
```
